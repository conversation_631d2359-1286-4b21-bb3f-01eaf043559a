import 'package:flutter/material.dart';
import 'package:mevolvesupport/constants/app_config.dart';

/// Developer overlay widget that shows technical debugging info
/// Only visible in developer mode (debug builds)
/// Keeps the production UI completely clean for support employees
class DeveloperOverlay extends StatelessWidget {
  const DeveloperOverlay({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    if (AppConfig.isDeveloperMode) {
      return child;
    }

    return Stack(
      children: [
        child,
        // Developer overlay positioned at the top
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Material(
            color: Colors.black87,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              child: Row(
                children: [
                  // Data source indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      '🔥 FIRESTORE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  const Spacer(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
