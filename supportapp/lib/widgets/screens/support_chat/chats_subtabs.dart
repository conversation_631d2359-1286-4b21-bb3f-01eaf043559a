import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:mevolvesupport/cubits/app/app_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/providers/chats/chat_data_provider.dart';
import 'package:mevolvesupport/providers/chats/chat_data_state.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/filter_tab.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';

class ChatsSubtabs extends StatelessWidget {
  const ChatsSubtabs({super.key});

  // Available status tabs
  static const _statusTabs = [
    ChatStatus.notReplied,
    ChatStatus.clarify,
    ChatStatus.replied,
    ChatStatus.resolved,
  ];

  int _getCountForStatus(ChatStatus status, ChatsCount chatsCount) {
    switch (status) {
      case ChatStatus.notReplied:
        return chatsCount.notReplied;
      case ChatStatus.clarify:
        return chatsCount.clarify;
      case ChatStatus.replied:
        return chatsCount.replied;
      case ChatStatus.resolved:
        return chatsCount.resolved;
      default:
        return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<ChatDataProvider, ChatDataState>(
      builder: (context, chatDataState) {
        final chatsCount = chatDataState.totalCounts;
        final currentStatus = chatDataState.currentStatus;
        final supportPersonFilter = chatDataState.supportPersonFilter;

        // Update support tab count
        context.read<AppCubit>().updateSupportTabCount(
              count: chatsCount.notReplied,
            );

        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Status tabs
              ..._statusTabs
                  .map(
                    (status) => [
                      FilterTab(
                        onTap: () {
                          context.read<ChatDataProvider>().changeFilter(
                            status: status,
                          );
                        },
                        title: status.toReadableString(),
                        isSelected: currentStatus == status,
                        count: _getCountForStatus(status, chatsCount),
                      ),
                      if (status != _statusTabs.last) const SizedBox(width: 16),
                    ],
                  )
                  .expand((widgets) => widgets),

              const SizedBox(width: 24),

              // Divider
              SizedBox(
                height: 24,
                width: 2,
                child: VerticalDivider(
                  thickness: 2,
                  width: 2,
                  color: colorScheme.color10,
                ),
              ),

              const SizedBox(width: 24),

              // Support person filter
              _SupportPersonFilter(
                supportPersonFilter: supportPersonFilter,
              ),
            ],
          ),
        );
      },
    );
  }
}

// Separate widget for support person filter to reduce complexity
class _SupportPersonFilter extends StatelessWidget {
  const _SupportPersonFilter({
    required this.supportPersonFilter,
  });

  final String? supportPersonFilter;

  String _getFilterLabel(BuildContext context, String? filter) {
    if (filter == null || filter == 'all') {
      return 'All';
    } else if (filter == 'new') {
      return 'New chat';
    } else {
      // Find support user name
      final supportUsers = context.read<AppCubit>().state.supportUsers ?? [];
      final user = supportUsers.firstWhereOrNull((u) => u.sid == filter);
      return user != null ? user.sname.split(' ')[0] : 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final supportUsers = context.read<AppCubit>().state.supportUsers ?? [];
    final filterLabel = _getFilterLabel(context, supportPersonFilter);

    return ClipRRect(
      borderRadius: BorderRadius.circular(30),
      child: Material(
        color: colorScheme.color5,
        child: PopupMenuButton(
          offset: const Offset(-20, 45),
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          color: colorScheme.color31,
          constraints: const BoxConstraints(minWidth: 247),
          itemBuilder: (context) => [
            PopupMenuItem(
              enabled: false,
              padding: EdgeInsets.zero,
              child: _FilterPopupContent(
                supportUsers: supportUsers,
                currentFilter: supportPersonFilter,
                onApply: (filter) {
                  context.read<ChatDataProvider>().changeFilter(
                        supportPersonFilter: filter,
                      );
                  Navigator.pop(context);
                },
              ),
            ),
          ],
          child: FilterTab(
            title: filterLabel,
            titleUnselectedStyle: MeFontStyle.E7,
            titleSelectedStyle: MeFontStyle.E12,
            isSelected:
                supportPersonFilter != null && supportPersonFilter != 'all',
            tabSelectedColor: colorScheme.color1,
            borderRadius: 20,
            showDropdownIcon: true,
          ),
        ),
      ),
    );
  }
}

// Separate widget for popup content
class _FilterPopupContent extends StatefulWidget {
  const _FilterPopupContent({
    required this.supportUsers,
    required this.currentFilter,
    required this.onApply,
  });

  final List<SupportUser> supportUsers;
  final String? currentFilter;
  final Function(String?) onApply;

  @override
  State<_FilterPopupContent> createState() => _FilterPopupContentState();
}

class _FilterPopupContentState extends State<_FilterPopupContent> {
  late String? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.currentFilter;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final hasChanges = _selectedFilter != widget.currentFilter;

    // Build filter options
    final filterOptions = [
      (value: null as String?, label: 'All'),
      (value: 'new', label: 'New chat'),
      ...widget.supportUsers.take(widget.supportUsers.length - 1).map(
            (user) => (
              value: user.sid,
              label: user.sname.split(' ')[0],
            ),
          ),
    ];

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.6,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const MeText(
                  text: 'By',
                  meFontStyle: MeFontStyle.A1,
                ),
                MeIconButton(
                  key: const ValueKey('close'),
                  iconPath: Assets.svg.closeIcon.path,
                  onPressed: () => Navigator.pop(context),
                  buttonColor: colorScheme.color5,
                  iconColor: colorScheme.color7,
                ),
              ],
            ),
          ),

          Divider(
            color: colorScheme.color6,
            height: 2,
            thickness: 2,
          ),

          // Scrollable filter options
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: filterOptions
                    .map(
                      (option) => InkWell(
                        onTap: () {
                          setState(() {
                            _selectedFilter = option.value;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8.0,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              MeText(
                                text: option.label,
                                meFontStyle: MeFontStyle.C8,
                              ),
                              MeRadioButton(
                                value: option.value,
                                groupValue: _selectedFilter,
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),

          // Sticky footer with Apply button
          Container(
            decoration: BoxDecoration(
              color: colorScheme.color31,
              border: Border(
                top: BorderSide(
                  color: colorScheme.color6,
                  width: 2,
                ),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Material(
                    borderRadius: BorderRadius.circular(6),
                    color: hasChanges ? colorScheme.color1 : colorScheme.color4,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(6),
                      splashFactory: InkSparkle.splashFactory,
                      onTap: hasChanges
                          ? () => widget.onApply(_selectedFilter)
                          : null,
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: MeText(
                          text: 'Apply',
                          meFontStyle:
                              hasChanges ? MeFontStyle.D12 : MeFontStyle.D23,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
