import 'package:equatable/equatable.dart';

class ChatsCount extends Equatable {
  factory ChatsCount.fromFirestore(Map<String, dynamic> map) {
    return ChatsCount(
      notReplied: map['notReplied'] as int,
      replied: map['replied'] as int,
      clarify: map['clarify'] as int,
      resolved: map['resolved'] as int,
    );
  }
  const ChatsCount({
    required this.notReplied,
    required this.replied,
    required this.clarify,
    required this.resolved,
  });

  final int notReplied;
  final int replied;
  final int clarify;
  final int resolved;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'notReplied': notReplied,
      'replied': replied,
      'clarify': clarify,
      'resolved': resolved,
    };
  }

  ChatsCount copyWith({
    int? notReplied,
    int? replied,
    int? clarify,
    int? resolved,
  }) {
    return ChatsCount(
      notReplied: notReplied ?? this.notReplied,
      replied: replied ?? this.replied,
      clarify: clarify ?? this.clarify,
      resolved: resolved ?? this.resolved,
    );
  }

  @override
  List<Object> get props => [
        notReplied,
        replied,
        clarify,
        resolved,
      ];
}
